import { Server } from '@hapi/hapi';
import { API_CONSTANTS } from '../common/constants';

export async function registerHealthRoutes(server: Server): Promise<void> {
  // Health check route
  server.route({
    method: 'GET',
    path: API_CONSTANTS.HEALTH_PATH,
    handler: async (_request, h) => {
      return h
        .response({
          status: 'healthy',
          service: 'service-haven-customer-verification',
          version: '1.0.0',
          timestamp: new Date().toISOString(),
        })
        .code(200);
    },
    options: {
      description: 'Service health check',
      tags: ['api', 'health'],
    },
  });

  console.log('Health routes registered');
}
