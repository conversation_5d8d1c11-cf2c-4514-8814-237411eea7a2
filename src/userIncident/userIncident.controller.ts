import { Request, ResponseToolkit, ResponseObject } from '@hapi/hapi';
import { UserIncidentService } from './userIncident.service';
import { getErrorMessage } from '../common/utils';
import {
  createUserIncidentSchema,
  updateUserIncidentSchema,
  getUserIncidentsQuerySchema,
  CreateUserIncidentRequest,
  UpdateUserIncidentRequest,
  GetUserIncidentsQuery,
} from './userIncident.dto';

export class UserIncidentController {
  private service: UserIncidentService;

  constructor() {
    this.service = new UserIncidentService();
  }

  /**
   * Creates a new user incident
   */
  async createIncident(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { error, value } = createUserIncidentSchema.validate(request.payload);
      if (error) {
        return h.response({ error: error.details[0].message }).code(400);
      }

      const incident = await this.service.createIncident(value as CreateUserIncidentRequest);
      return h.response(incident).code(201);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets a user incident by ID
   */
  async getIncidentById(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const incidentId = parseInt(id, 10);

      if (isNaN(incidentId)) {
        return h.response({ error: 'Invalid incident ID' }).code(400);
      }

      const incident = await this.service.getIncidentById(incidentId);

      if (!incident) {
        return h.response({ error: 'Incident not found' }).code(404);
      }

      return h.response(incident).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets user incidents with filtering and pagination
   */
  async getIncidents(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { error, value } = getUserIncidentsQuerySchema.validate(request.query);
      if (error) {
        return h.response({ error: error.details[0].message }).code(400);
      }

      const incidents = await this.service.getIncidents(value as GetUserIncidentsQuery);
      return h.response(incidents).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets all incidents for a specific user
   */
  async getIncidentsByUserId(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { userId } = request.params;
      const incidents = await this.service.getIncidentsByUserId(userId);
      return h.response(incidents).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Updates a user incident
   */
  async updateIncident(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const incidentId = parseInt(id, 10);

      if (isNaN(incidentId)) {
        return h.response({ error: 'Invalid incident ID' }).code(400);
      }

      const { error, value } = updateUserIncidentSchema.validate(request.payload);
      if (error) {
        return h.response({ error: error.details[0].message }).code(400);
      }

      const incident = await this.service.updateIncident(
        incidentId,
        value as UpdateUserIncidentRequest
      );

      if (!incident) {
        return h.response({ error: 'Incident not found' }).code(404);
      }

      return h.response(incident).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Deletes a user incident
   */
  async deleteIncident(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const incidentId = parseInt(id, 10);

      if (isNaN(incidentId)) {
        return h.response({ error: 'Invalid incident ID' }).code(400);
      }

      const deleted = await this.service.deleteIncident(incidentId);

      if (!deleted) {
        return h.response({ error: 'Incident not found' }).code(404);
      }

      return h.response({ message: 'Incident deleted successfully' }).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Assigns an incident to a user (sets status to IN_PROGRESS)
   */
  async assignIncident(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const incidentId = parseInt(id, 10);

      if (isNaN(incidentId)) {
        return h.response({ error: 'Invalid incident ID' }).code(400);
      }

      const { updatedBy } = request.payload as { updatedBy: string };

      if (!updatedBy) {
        return h.response({ error: 'updatedBy is required' }).code(400);
      }

      const incident = await this.service.assignIncident(incidentId, updatedBy);

      if (!incident) {
        return h.response({ error: 'Incident not found' }).code(404);
      }

      return h.response(incident).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Resolves an incident (sets status to CLOSED)
   */
  async resolveIncident(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const incidentId = parseInt(id, 10);

      if (isNaN(incidentId)) {
        return h.response({ error: 'Invalid incident ID' }).code(400);
      }

      const { updatedBy } = request.payload as { updatedBy: string };

      if (!updatedBy) {
        return h.response({ error: 'updatedBy is required' }).code(400);
      }

      const incident = await this.service.resolveIncident(incidentId, updatedBy);

      if (!incident) {
        return h.response({ error: 'Incident not found' }).code(404);
      }

      return h.response(incident).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets incident statistics
   */
  async getStatistics(_request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const statistics = await this.service.getStatistics();
      return h.response(statistics).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }
}
