import { UserIncident, Prisma } from '../../prisma/generated';
import {
  CreateUserIncidentRequest,
  UpdateUserIncidentRequest,
  UserIncidentResponse,
  PaginatedUserIncidentsResponse,
} from './userIncident.dto';

export class UserIncidentMapper {
  /**
   * Maps a CreateUserIncidentRequest DTO to a UserIncident entity
   */
  static toEntity(dto: CreateUserIncidentRequest): Prisma.UserIncidentCreateInput {
    return {
      booking_refrence_number: dto.bookingReferenceNumber,
      lead_guest_name: dto.leadGuestName,
      reason_for_ban: dto.reasonForBan,
      is_discovered_after_departure: dto.isDiscoveredAfterDeparture,
      is_police_involved: dto.isPoliceInvolved,
      crime_reference_number: dto.crimeReferenceNumber,
      other_information: dto.otherInformation,
      created_by: dto.createdBy,
      status: 'PENDING',
    };
  }

  /**
   * Maps an UpdateUserIncidentRequest DTO to a UserIncident entity update
   */
  static toUpdateEntity(dto: UpdateUserIncidentRequest): Prisma.UserIncidentUpdateInput {
    const updateData: Prisma.UserIncidentUpdateInput = {};

    if (dto.bookingReferenceNumber !== undefined) {
      updateData.booking_refrence_number = dto.bookingReferenceNumber;
    }
    if (dto.leadGuestName !== undefined) {
      updateData.lead_guest_name = dto.leadGuestName;
    }
    if (dto.reasonForBan !== undefined) {
      updateData.reason_for_ban = dto.reasonForBan;
    }
    if (dto.isDiscoveredAfterDeparture !== undefined) {
      updateData.is_discovered_after_departure = dto.isDiscoveredAfterDeparture;
    }
    if (dto.isPoliceInvolved !== undefined) {
      updateData.is_police_involved = dto.isPoliceInvolved;
    }
    if (dto.crimeReferenceNumber !== undefined) {
      updateData.crime_reference_number = dto.crimeReferenceNumber;
    }
    if (dto.otherInformation !== undefined) {
      updateData.other_information = dto.otherInformation;
    }
    if (dto.status !== undefined) {
      updateData.status = dto.status;
      // Set resolved_at when status changes to CLOSED
      if (dto.status === 'CLOSED') {
        updateData.resolved_at = new Date();
      }
    }
    if (dto.updatedBy !== undefined) {
      updateData.update_by = dto.updatedBy;
    }

    return updateData;
  }

  /**
   * Maps a UserIncident entity to a UserIncidentResponse DTO
   */
  static toResponse(entity: UserIncident): UserIncidentResponse {
    return {
      id: entity.id,
      bookingReferenceNumber: entity.booking_refrence_number,
      leadGuestName: entity.lead_guest_name,
      reasonForBan: entity.reason_for_ban,
      isDiscoveredAfterDeparture: entity.is_discovered_after_departure,
      isPoliceInvolved: entity.is_police_involved || undefined,
      crimeReferenceNumber: entity.crime_reference_number || undefined,
      otherInformation: entity.other_information || undefined,
      createdBy: entity.created_by,
      updatedBy: entity.update_by || undefined,
      status: entity.status,
      createdAt: entity.created_at.toISOString(),
      updatedAt: entity.updated_at?.toISOString(),
      resolvedAt: entity.resolved_at?.toISOString(),
    };
  }

  /**
   * Maps an array of UserIncident entities to UserIncidentResponse DTOs
   */
  static toResponseArray(entities: UserIncident[]): UserIncidentResponse[] {
    return entities.map(entity => this.toResponse(entity));
  }

  /**
   * Maps UserIncident entities with pagination info to PaginatedUserIncidentsResponse
   */
  static toPaginatedResponse(
    entities: UserIncident[],
    page: number,
    limit: number,
    total: number
  ): PaginatedUserIncidentsResponse {
    return {
      incidents: this.toResponseArray(entities),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
