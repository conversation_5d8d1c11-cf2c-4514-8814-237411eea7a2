import { PrismaClient, UserIncident, Prisma } from '../../prisma/generated';
import { GetUserIncidentsQuery } from './userIncident.dto';

export class UserIncidentRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Creates a new user incident
   */
  async create(incidentData: Prisma.UserIncidentCreateInput): Promise<UserIncident> {
    return await this.prisma.userIncident.create({
      data: incidentData,
    });
  }

  /**
   * Finds a user incident by ID
   */
  async findById(id: number): Promise<UserIncident | null> {
    return await this.prisma.userIncident.findUnique({
      where: { id },
    });
  }

  /**
   * Finds user incidents with filtering, pagination, and sorting
   */
  async findMany(
    query: GetUserIncidentsQuery
  ): Promise<{ incidents: UserIncident[]; total: number }> {
    const where: Prisma.UserIncidentWhereInput = {};

    // Build filter object
    if (query.status) {
      where.status = query.status;
    }
    if (query.createdBy) {
      where.created_by = query.createdBy;
    }

    // Build sort object
    const sortField = query.sortBy || 'created_at';
    const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
    const orderBy: Prisma.UserIncidentOrderByWithRelationInput = { [sortField]: sortOrder };

    // Pagination
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    // Execute queries
    const [incidents, total] = await Promise.all([
      this.prisma.userIncident.findMany({
        where,
        orderBy,
        skip,
        take: limit,
      }),
      this.prisma.userIncident.count({ where }),
    ]);

    return { incidents, total };
  }

  /**
   * Finds user incidents by created by user
   */
  async findByUserId(userId: string): Promise<UserIncident[]> {
    return await this.prisma.userIncident.findMany({
      where: { created_by: userId },
      orderBy: { created_at: 'desc' },
    });
  }

  /**
   * Updates a user incident by ID
   */
  async updateById(
    id: number,
    updateData: Prisma.UserIncidentUpdateInput
  ): Promise<UserIncident | null> {
    try {
      return await this.prisma.userIncident.update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      return null; // Record not found or other error
    }
  }

  /**
   * Deletes a user incident by ID
   */
  async deleteById(id: number): Promise<UserIncident | null> {
    try {
      return await this.prisma.userIncident.delete({
        where: { id },
      });
    } catch (error) {
      return null; // Record not found or other error
    }
  }

  /**
   * Finds incidents by status
   */
  async findByStatus(status: 'PENDING' | 'IN_PROGRESS' | 'CLOSED'): Promise<UserIncident[]> {
    return await this.prisma.userIncident.findMany({
      where: { status },
      orderBy: { created_at: 'desc' },
    });
  }

  /**
   * Gets incident statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
  }> {
    const [total, statusStats] = await Promise.all([
      this.prisma.userIncident.count(),
      this.prisma.userIncident.groupBy({
        by: ['status'],
        _count: {
          status: true,
        },
      }),
    ]);

    const byStatus: Record<string, number> = {};
    statusStats.forEach(stat => {
      byStatus[stat.status] = stat._count.status;
    });

    return { total, byStatus };
  }
}
