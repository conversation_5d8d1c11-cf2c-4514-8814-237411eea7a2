import Hapi from '@hapi/hapi';
import * as dotenv from 'dotenv';
import { DatabaseConnection } from './common/utils/database';
import { registerRoutes } from './routes';
import { SERVER_CONSTANTS } from './common/constants';

// Load environment variables
dotenv.config();

export class Server {
  private server!: Hapi.Server;
  private database: DatabaseConnection;

  constructor() {
    this.database = DatabaseConnection.getInstance();
  }

  /**
   * Initializes the Hapi server
   */
  private async initializeServer(): Promise<void> {
    const port = process.env.PORT || SERVER_CONSTANTS.DEFAULT_PORT;
    const host = process.env.HOST || SERVER_CONSTANTS.DEFAULT_HOST;

    this.server = Hapi.server({
      port,
      host,
      routes: {
        timeout: {
          server: SERVER_CONSTANTS.REQUEST_TIMEOUT,
        },
        payload: {
          maxBytes: SERVER_CONSTANTS.MAX_PAYLOAD_SIZE,
        },
        cors: {
          origin: ['*'], //TODO for production
          headers: ['Accept', 'Authorization', 'Content-Type', 'If-None-Match', 'X-Requested-With'],
          additionalHeaders: ['cache-control', 'x-requested-with'],
          credentials: true,
        },
      },
    });

    // Add request logging
    this.server.events.on('request', (request, event) => {
      if (event.error) {
        console.error('Request error:', {
          method: request.method,
          url: request.url.pathname,
          error: event.error instanceof Error ? event.error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Add response logging
    this.server.events.on('response', request => {
      console.log('Request completed:', {
        method: request.method,
        url: request.url.pathname,
        statusCode: (request.response as { statusCode?: number })?.statusCode,
        responseTime: Date.now() - request.info.received,
        timestamp: new Date().toISOString(),
      });
    });

    console.log('Hapi server initialized');
  }

  /**
   * Connects to the database
   */
  private async connectDatabase(): Promise<void> {
    try {
      await this.database.connect();
      console.log('Database connection established');
    } catch (error) {
      console.error('Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * Registers all routes
   */
  private async registerRoutes(): Promise<void> {
    try {
      await registerRoutes(this.server);
    } catch (error) {
      console.error('Failed to register routes:', error);
      throw error;
    }
  }

  /**
   * Starts the server
   */
  public async start(): Promise<void> {
    try {
      console.log('Starting Service Haven Customer Verification API...');

      // Initialize server
      await this.initializeServer();

      // Connect to database
      await this.connectDatabase();

      // Register routes
      await this.registerRoutes();

      // Start the server
      await this.server.start();

      console.log(`Server running on ${this.server.info.uri}`);
      console.log('Service Haven Customer Verification API started successfully');
    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Stops the server gracefully
   */
  public async stop(): Promise<void> {
    try {
      console.log('Stopping server...');

      // Stop the server
      if (this.server) {
        await this.server.stop();
        console.log('Server stopped');
      }

      // Disconnect from database
      await this.database.disconnect();
      console.log('Database disconnected');

      console.log('Server stopped gracefully');
    } catch (error) {
      console.error('Error stopping server:', error);
      throw error;
    }
  }

  /**
   * Gets server instance
   */
  public getServer(): Hapi.Server {
    return this.server;
  }

  /**
   * Gets server info
   */
  public getServerInfo(): {
    uri: string;
    host: string;
    port: number;
    protocol: string;
  } {
    return {
      uri: this.server.info.uri,
      host: this.server.info.host,
      port:
        typeof this.server.info.port === 'number'
          ? this.server.info.port
          : parseInt(this.server.info.port as string),
      protocol: this.server.info.protocol,
    };
  }

  /**
   * Performs health check
   */
  public async healthCheck(): Promise<{
    server: { status: string; uptime: number };
    database: { status: string; responseTime?: number; error?: string };
  }> {
    const serverUptime = process.uptime();
    const dbHealth = await this.database.healthCheck();

    return {
      server: {
        status: 'healthy',
        uptime: serverUptime,
      },
      database: dbHealth,
    };
  }
}

// Handle graceful shutdown
const gracefulShutdown = async (server: Server) => {
  console.log('Received shutdown signal');
  try {
    await server.stop();
    process.exit(0);
  } catch (error) {
    console.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Start the server if this file is run directly
if (require.main === module) {
  const server = new Server();

  // Handle process signals for graceful shutdown
  process.on('SIGINT', () => gracefulShutdown(server));
  process.on('SIGTERM', () => gracefulShutdown(server));

  // Handle uncaught exceptions
  process.on('uncaughtException', error => {
    console.error('Uncaught Exception:', error);
    gracefulShutdown(server);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown(server);
  });

  // Start the server
  server.start().catch(error => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}

export default Server;
