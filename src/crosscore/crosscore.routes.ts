import { Server } from '@hapi/hapi';
import { CrosscoreController } from './crosscore.controller';
import { API_CONSTANTS } from '../common/constants';

export async function registerCrosscoreRoutes(server: Server): Promise<void> {
  // Initialize controller
  const crosscoreController = new CrosscoreController();

  // CrossCore routes
  server.route([]);

  console.log('CrossCore routes registered');
}
