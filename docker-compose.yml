version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456789
      POSTGRES_DB: mydb
    ports:
      - '5432:5432'
    volumes:
      - pgdata:/var/lib/postgresql/data

  app:
    build: .
    container_name: app
    depends_on:
      - postgres
    environment:
      DATABASE_URL: *******************************************/mydb
    ports:
      - '3000:3000'
    # Make sure app tries to connect to hostname `postgres`, not `localhost`
    # i.e. use host: `postgres` in your DB config

volumes:
  pgdata:
