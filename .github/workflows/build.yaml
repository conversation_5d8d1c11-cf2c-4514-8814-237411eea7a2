name: Haven Customer Verification Service

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

env:
  ECR_REGISTRY_TOOLING: '745662293263.dkr.ecr.eu-west-1.amazonaws.com'
  APP_NAME: 'service-haven-customer-verification'
  BRAND: 'haven'
  PRODUCT: 'customer-data'
  TRIBE: 'foundation'

jobs:
  checks:
    name: Checks
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v3
        env:
          NODE_AUTH_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT  }}
        with:
          node-version-file: '.nvmrc'
          registry-url: https://npm.pkg.github.com
          scope: '@havenengineering'
          cache: 'npm'

      - name: Install
        env:
          NODE_AUTH_TOKEN: ${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT  }}
        run: npm ci

      - name: Prisma Generate
        run: npx prisma generate

      - name: Type check
        run: npm run type-check

      - name: Lint
        run: npm run lint

      # - name: Test
      #   run: npm run test

  build:
    name: Build docker image
    if: github.ref != 'refs/heads/main'
    runs-on: platform-runners-ecr
    needs: checks

    concurrency:
      group: docker-publishing-${{ github.ref }}
      cancel-in-progress: true

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Build Docker image
        env:
          DOCKER_BUILDKIT: 1
        run: docker build . --progress=plain --build-arg GITHUB_PKG_TOKEN=${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }} --build-arg APP_VERSION=0.0.0 -t github-actions-build

  publish:
    name: Publish docker image
    if: github.ref == 'refs/heads/main'
    runs-on: platform-runners-ecr
    needs: checks

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Login to Docker registry
        run: |
          aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin ${ECR_REGISTRY_TOOLING}

      - name: Generate APP_VERSION for Release
        run: |
          BUILD_TIMESTAMP=$(date +'%y%m%d%H%M');
          echo "APP_VERSION=${BUILD_TIMESTAMP}" >> $GITHUB_ENV

      - name: Generate DOCKER_TAG for Release
        run: |
          echo "DOCKER_TAG_TOOLING=${ECR_REGISTRY_TOOLING}/customer-verification/${APP_NAME}:${APP_VERSION}" >> $GITHUB_ENV

      - name: Build and Push Docker image
        env:
          DOCKER_BUILDKIT: 1
        run: |
          docker build . --progress=plain --build-arg GITHUB_PKG_TOKEN=${{ secrets.HAVEN_PLATFORM_BOT_PACKAGE_READ_PAT }} --build-arg APP_VERSION=${APP_VERSION} -t ${DOCKER_TAG_TOOLING}
          docker push ${DOCKER_TAG_TOOLING}
