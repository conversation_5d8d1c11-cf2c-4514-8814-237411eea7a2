// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../prisma/generated"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model UserIncident {
  id                                Int   @id @default(autoincrement())
  booking_refrence_number           String
  lead_guest_name                   String
  reason_for_ban                    String
  is_discovered_after_departure     Boolean 
  is_police_involved                Boolean?
  crime_reference_number            String?
  other_information                 String?
  created_by                        String
  update_by                         String?
  created_at                        DateTime @default(now())
  updated_at                        DateTime? @updatedAt
  resolved_at                       DateTime?
  status                            UserIncidentStatus   @default(PENDING)
  attachments                       Attachment[] @relation("UserIncidentToAttachment")

  @@index([created_at])
  @@map("user_incidents")
}

model Attachment {
  id                 Int   @id @default(autoincrement())
  user_incident_id   Int
  file_name          String
  file_url           String
  created_at         DateTime @default(now())
  updated_at         DateTime? @updatedAt

  @@index([user_incident_id])
  @@map("attachments")

  userIncident    UserIncident @relation("UserIncidentToAttachment", fields: [user_incident_id], references: [id])
}

enum UserIncidentStatus {
  PENDING
  IN_PROGRESS
  CLOSED
}

model CrosscoreRequest {
  id               Int   @id @default(autoincrement())
  userId           String
  requestId        String   @unique
  verificationType String
  status           String   @default("pending")
  requestData      Json
  responseData     Json?
  experianRequestId String?
  experianStatus   String?
  errorMessage     String?
  webhookReceived  Boolean  @default(false)
  webhookData      Json?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  completedAt      DateTime?

  @@index([userId, status])
  @@index([createdAt])
  @@index([status, verificationType])
  @@index([experianRequestId])
  @@map("crosscore_requests")
}
